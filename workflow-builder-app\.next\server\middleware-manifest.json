{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "fca6cea52281dbcc7f881ae9161f5d35", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "74ca0e1c603c76f7958e4f8c0fc573d8e60763abb37ee1a22b208f11c37b1664", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ac69295bcf8a8c5f9d32168c8b587e66f58bf3d10dd60ad45ca9eb5446a1324b"}}}, "sortedMiddleware": ["/"], "functions": {}}