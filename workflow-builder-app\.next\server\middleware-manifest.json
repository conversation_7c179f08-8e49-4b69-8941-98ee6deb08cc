{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "2b60736bfa8bf50fd8534aacc9a55118", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d0d2e2f90642c15407befa0db67d937b8eab3e6cbe68b07fe40303e4ec463c0f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "35a4035d72208a207d573292bcc65cc17e0059fabda0be586ed7a67ac025ad03"}}}, "sortedMiddleware": ["/"], "functions": {}}