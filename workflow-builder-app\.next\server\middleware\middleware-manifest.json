{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\.[\\w]+$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "6077fbcb5d9374b5cc8a7b0dcd282978", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "258376220a5a442288f7a266659cf9f46e5a489f559e6cc72c8866f61f4970a7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8bef6a34aefbb72f6e7e59064e677b0bee03e224cbe219377db35982c69eee08"}}}, "instrumentation": null, "functions": {}}