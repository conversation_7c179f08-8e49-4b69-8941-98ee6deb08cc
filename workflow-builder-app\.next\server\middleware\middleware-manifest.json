{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\.[\\w]+$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "4d4bcd8acb7e673bc3350f3b46379657", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e678360e62cf2892d7b0ef17d50ef6754a9935794876bd5e44e2a5fca635f15b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f6a6bba2ccfd3cbe622706fd14d42289a18e7d1ae9978852493b02b30c21a124"}}}, "instrumentation": null, "functions": {}}